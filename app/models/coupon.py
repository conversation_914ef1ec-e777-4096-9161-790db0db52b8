import enum
from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, Enum, DateTime, ForeignKey, Float, JSON
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.enum import Status


class CouponType(enum.Enum):
    """优惠券类型枚举
    定义了系统中所有支持的优惠券类型
    """
    COUPON = "coupon"  # 基础优惠券
    DISCOUNT = "discount"  # 折扣券
    CASH = "cash"  # 现金券
    FULL_REDUCTION = "full_reduction"  # 满减券


class CouponScope(enum.Enum):
    """优惠券作用范围枚举
    定义了优惠券的作用范围
    """
    ORDER = "order"  # 订单范围
    PRODUCT = "product"  # 商品范围


class CouponUsageStatus(enum.Enum):
    """优惠券使用状态枚举
    定义了优惠券的使用状态
    """
    VALID = "valid"  # 有效
    INVALID = "invalid"  # 无效
    NOT_STARTED = "not_started"  # 未生效
    EXPIRED = "expired"  # 已过期
    USED = "used"  # 已使用

class CouponUsageCycle(enum.Enum):
    """优惠券使用周期枚举
    定义了优惠券的使用周期
    """
    PER_ORDER = "per_order"  # 每次订单
    PER_DAY = "per_day"  # 每天
    PER_WEEK = "per_week"  # 每周
    PER_MONTH = "per_month"  # 每月
    PER_YEAR = "per_year"  # 每年


class PaymentChannel(enum.Enum):
    """适用支付渠道枚举
    定义了优惠券适用的支付渠道
    """
    WECHAT = "wechat"  # 微信
    PERSONAL_BALANCE = "personal_balance"  # 个人账户余额
    ENTERPRISE_BALANCE = "enterprise_balance"  # 企业账户余额


class DistributionChannel(enum.Enum):
    """发放渠道枚举
    定义了优惠券的发放渠道
    """
    NEW_USER = "new_user"  # 新人注册
    VIEW_ACTIVITY = "view_activity"  # 浏览活动
    SHARE_ACTIVITY = "share_activity"  # 分享活动
    PURCHASE = "purchase"  # 销售购买

class CouponUsageRecord(Base):
    """优惠券使用记录
    记录优惠券的使用情况，包括使用者、使用时间、使用订单等信息
    """
    __tablename__ = "coupon_usage_records"

    id = Column(Integer, primary_key=True, index=True, comment="记录ID")
    coupon_id = Column(Integer, ForeignKey("coupons.id"), nullable=False, comment="优惠券ID")
    coupon_batch_id = Column(Integer, ForeignKey("coupon_batches.id"), nullable=False, comment="优惠券批次ID")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="使用者ID")
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=True, default=None, comment="订单ID")
    order_item_id = Column(Integer, ForeignKey("order_items.id"), nullable=True, default=None, comment="订单项ID")
    discount_amount = Column(Float, nullable=False, default=0.0, comment="优惠金额")
    used_at = Column(DateTime, default=datetime.now, comment="使用时间")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    status = Column(Enum(CouponUsageStatus), nullable=False, default=CouponUsageStatus.VALID, comment="优惠券使用状态")
    # 关联关系
    user = relationship("User", back_populates="coupon_usage_records")
    coupon = relationship("Coupon", back_populates="usage_records")
    coupon_batch = relationship("CouponBatch", back_populates="usage_records")
    order = relationship("Order", back_populates="coupon_usage_records")
    order_item = relationship("OrderItem", back_populates="coupon_usage_records")


class Coupon(Base):
    """优惠券基类
    所有具体的优惠券类型都继承自这个类
    """
    __tablename__ = "coupons"

    id = Column(Integer, primary_key=True, index=True, comment="优惠券ID")
    name = Column(String(100), nullable=False, comment="优惠券名称")
    description = Column(Text, comment="优惠券描述")
    type = Column(Enum(CouponType), nullable=False, default=CouponType.COUPON, comment="优惠券类型")
    
    # 条件相关字段
    condition_scope = Column(Enum(CouponScope), nullable=True, default=CouponScope.ORDER, comment="条件范围")
    condition_objects = Column(JSON, nullable=True, default=list, comment="条件对象组合")
    condition_amount = Column(Float, nullable=True, default=0, comment="条件对象金额")
    usage_quantity = Column(Integer, nullable=True, default=0, comment="周期可使用数量")
    usage_cycle = Column(Enum(CouponUsageCycle), nullable=True, default=CouponUsageCycle.PER_ORDER, comment="使用周期")
    usage_limit = Column(Integer, nullable=True, default=0, comment="使用限制")
    mutual_exclusive_rules = Column(JSON, nullable=True, default=list, comment="互斥规则")
    payment_channels = Column(JSON, nullable=True, default=list, comment="支付渠道")

    # 作用相关字段
    apply_scope = Column(Enum(CouponScope), nullable=False, default=CouponScope.ORDER, comment="作用范围")
    apply_objects = Column(JSON, nullable=True, default=list, comment="作用对象组合")
    apply_order = Column(Integer, nullable=True, default=0, comment="计算顺序")
    
    # 基本信息字段
    status = Column(Enum(Status), nullable=False, default=Status.ACTIVE, comment="状态")
    
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    usage_records = relationship("CouponUsageRecord", back_populates="coupon")
    batches = relationship("CouponBatch", back_populates="coupon")

    __mapper_args__ = {
        "polymorphic_on": type,
        "polymorphic_identity": CouponType.COUPON
    }


class DiscountCoupon(Coupon):
    """折扣券
    用于按比例折扣的优惠券
    """
    __tablename__ = "discount_coupons"

    id = Column(Integer, ForeignKey("coupons.id"), primary_key=True, comment="关联的基础优惠券ID")
    discount_rate = Column(Float, nullable=False, default=1, comment="折扣比例（0-1之间的小数）")
    min_amount = Column(Float, nullable=False, default=0, comment="最低消费金额")
    max_discount = Column(Float, nullable=False, default=0, comment="最大折扣金额")

    __mapper_args__ = {
        "polymorphic_identity": CouponType.DISCOUNT
    }


class CashCoupon(Coupon):
    """现金券
    用于直接抵扣金额的优惠券
    """
    __tablename__ = "cash_coupons"

    id = Column(Integer, ForeignKey("coupons.id"), primary_key=True, comment="关联的基础优惠券ID")
    amount = Column(Float, nullable=False, default=0, comment="抵扣金额")

    __mapper_args__ = {
        "polymorphic_identity": CouponType.CASH
    }


class FullReductionCoupon(Coupon):
    """满减券
    用于满额减价的优惠券
    """
    __tablename__ = "full_reduction_coupons"

    id = Column(Integer, ForeignKey("coupons.id"), primary_key=True, comment="关联的基础优惠券ID")
    full_amount = Column(Float, nullable=False, default=0, comment="满足金额")
    reduction_amount = Column(Float, nullable=False, default=0, comment="满减金额")

    __mapper_args__ = {
        "polymorphic_identity": CouponType.FULL_REDUCTION
    }


class CouponBatch(Base):
    """优惠券批次
    管理优惠券的批次发放和领取规则
    """
    __tablename__ = "coupon_batches"

    id = Column(Integer, primary_key=True, index=True, comment="批次ID")
    name = Column(String(100), nullable=False, comment="批次名称")
    description = Column(Text, comment="批次描述")
    batch_number = Column(Integer, nullable=False, comment="批次")
    quantity = Column(Integer, nullable=False, default=0, comment="数量")
    start_time = Column(DateTime, nullable=False, default=datetime.now, comment="生效时间")
    end_time = Column(DateTime, nullable=False, default=datetime.now, comment="结束时间")
    valid_duration = Column(Integer, nullable=False, default=0, comment="有效时长(小时)")
    coupon_id = Column(Integer, ForeignKey("coupons.id"), nullable=False, comment="优惠券ID")
    status = Column(Enum(Status), nullable=False, default=Status.ACTIVE, comment="状态")
    
    # 发放相关字段
    distribution_channels = Column(JSON, nullable=False, default=list, comment="发放渠道")
    distribution_quantity = Column(Integer, nullable=False, default=0, comment="周期发放数量")
    distribution_cycle = Column(Enum(CouponUsageCycle), nullable=False, default=CouponUsageCycle.PER_DAY, comment="发放周期")

    # 领取相关字段
    receive_quantity = Column(Integer, nullable=False, default=0, comment="周期可领取数量")
    receive_cycle = Column(Enum(CouponUsageCycle), nullable=False, default=CouponUsageCycle.PER_DAY, comment="领取周期")
    receive_start_time = Column(DateTime, nullable=False, default=datetime.now, comment="领取开始时间")
    receive_end_time = Column(DateTime, nullable=False, default=datetime.now, comment="领取结束时间")
    
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联关系
    coupon = relationship("Coupon", back_populates="batches")
    usage_records = relationship("CouponUsageRecord", back_populates="coupon_batch")
