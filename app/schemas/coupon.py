from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, Field, field_serializer, field_validator, ConfigDict

from app.models.coupon import CouponType, CouponScope, CouponUsageCycle, PaymentChannel
from app.models.enum import Status


class CouponBase(BaseModel):
    """优惠券基础模型"""
    name: str = Field(..., description="优惠券名称")
    description: Optional[str] = Field(None, description="优惠券描述")

    # 条件相关字段
    condition_scope: CouponScope = Field(CouponScope.ORDER, description="条件范围")
    condition_objects: List[Dict[str, Any]] = Field(default_factory=list, description="条件对象组合")
    condition_amount: float = Field(0, description="条件对象金额")
    usage_quantity: int = Field(0, description="周期可使用数量")
    usage_cycle: CouponUsageCycle = Field(CouponUsageCycle.PER_ORDER, description="使用周期")
    usage_limit: int = Field(0, description="使用限制")
    mutual_exclusive_rules: List[int] = Field(default_factory=list, description="互斥规则")
    payment_channels: List[PaymentChannel] = Field(default_factory=list, description="支付渠道")

    # 作用相关字段
    apply_scope: CouponScope = Field(CouponScope.ORDER, description="优惠券作用范围")
    apply_objects: List[Dict[str, Any]] = Field(default_factory=list, description="作用对象组合")
    apply_order: int = Field(0, description="计算顺序")

    status: Status = Field(Status.ACTIVE, description="优惠券状态")
    type: CouponType = Field(..., description="优惠券类型")


class CouponCreate(CouponBase):
    """创建优惠券请求模型"""
    pass


class CouponUpdate(BaseModel):
    """更新优惠券请求模型"""
    name: Optional[str] = Field(None, description="优惠券名称")
    description: Optional[str] = Field(None, description="优惠券描述")

    # 条件相关字段
    condition_scope: Optional[CouponScope] = Field(None, description="条件范围")
    condition_objects: Optional[List[Dict[str, Any]]] = Field(None, description="条件对象组合")
    condition_amount: Optional[float] = Field(None, description="条件对象金额")
    usage_quantity: Optional[int] = Field(None, description="周期可使用数量")
    usage_cycle: Optional[CouponUsageCycle] = Field(None, description="使用周期")
    usage_limit: Optional[int] = Field(None, description="使用限制")
    mutual_exclusive_rules: Optional[List[int]] = Field(None, description="互斥规则")
    payment_channels: Optional[List[PaymentChannel]] = Field(None, description="支付渠道")

    # 作用相关字段
    apply_scope: Optional[CouponScope] = Field(None, description="优惠券作用范围")
    apply_objects: Optional[List[Dict[str, Any]]] = Field(None, description="作用对象组合")
    apply_order: Optional[int] = Field(None, description="计算顺序")

    status: Optional[Status] = Field(None, description="优惠券状态")


class CouponStatusUpdateRequest(BaseModel):
    """优惠券状态更新请求模型"""
    status: Status = Field(..., description="优惠券状态")


class CouponResponse(CouponBase):
    """优惠券响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")

    @field_serializer("condition_objects", "apply_objects", "mutual_exclusive_rules", "payment_channels")
    def serialize_json_fields(self, value):
        """序列化JSON字段，将None转换为空列表"""
        return value if value is not None else []

    @field_validator("condition_objects", "apply_objects", "mutual_exclusive_rules", "payment_channels", mode="before")
    @classmethod
    def validate_json_fields(cls, value):
        """验证JSON字段，将None转换为空列表"""
        return value if value is not None else []


# 折扣券模型
class DiscountCouponBase(CouponBase):
    """创建折扣券请求模型"""
    discount_rate: float = Field(..., ge=0, le=1, description="折扣比例（0-1之间的小数）")
    min_amount: Optional[float] = Field(0, ge=0, description="最低消费金额")
    max_discount: Optional[float] = Field(0, ge=0, description="最大折扣金额")


class DiscountCouponCreate(DiscountCouponBase):
    pass


class DiscountCouponUpdate(CouponUpdate):
    """创建折扣券请求模型"""
    discount_rate: Optional[float] = Field(None, ge=0, le=1, description="折扣比例（0-1之间的小数）")
    min_amount: Optional[float] = Field(0, ge=0, description="最低消费金额")
    max_discount: Optional[float] = Field(0, ge=0, description="最大折扣金额")


class DiscountCouponResponse(DiscountCouponBase, CouponResponse):
    """折扣券响应模型"""

    @field_validator("condition_objects", "apply_objects", "mutual_exclusive_rules", "payment_channels", mode="before")
    @classmethod
    def validate_json_fields(cls, value):
        """验证JSON字段，将None转换为空列表"""
        return value if value is not None else []


# 满减券模型
class CashCouponBase(CouponBase):
    """创建现金券请求模型"""
    amount: float = Field(..., ge=0, description="抵扣金额")


class CashCouponCreate(CashCouponBase):
    """创建现金券请求模型"""
    pass


class CashCouponUpdate(CouponUpdate):
    """创建现金券请求模型"""
    amount: Optional[float] = Field(None, ge=0, description="抵扣金额")


class CashCouponResponse(CashCouponBase, CouponResponse):
    """现金券响应模型"""

    @field_validator("condition_objects", "apply_objects", "mutual_exclusive_rules", "payment_channels", mode="before")
    @classmethod
    def validate_json_fields(cls, value):
        """验证JSON字段，将None转换为空列表"""
        return value if value is not None else []


# 满减券模型
class FullReductionCouponBase(CouponBase):
    """创建满减券请求模型"""
    full_amount: float = Field(..., ge=0, description="满足金额")
    reduction_amount: float = Field(..., ge=0, description="满减金额")


class FullReductionCouponCreate(FullReductionCouponBase):
    """创建满减券请求模型"""
    pass


class FullReductionCouponUpdate(CouponUpdate):
    """创建满减券请求模型"""
    full_amount: Optional[float] = Field(None, ge=0, description="满足金额")
    reduction_amount: Optional[float] = Field(None, ge=0, description="满减金额")


class FullReductionCouponResponse(FullReductionCouponBase, CouponResponse):
    """满减券响应模型"""

    @field_validator("condition_objects", "apply_objects", "mutual_exclusive_rules", "payment_channels", mode="before")
    @classmethod
    def validate_json_fields(cls, value):
        """验证JSON字段，将None转换为空列表"""
        return value if value is not None else []


# 搜索相关模型
class CouponSearch(BaseModel):
    """优惠券搜索请求模型"""
    keyword: Optional[str] = Field(None, description="搜索关键词")
    status: Optional[Status] = Field(None, description="优惠券状态")
    type: Optional[CouponType] = Field(None, description="优惠券类型")
    page: int = Field(1, description="页码")
    pageSize: int = Field(10, description="每页数量")


class CouponListData(BaseModel):
    """优惠券列表数据模型"""
    total: int
    list: List[CouponResponse]

    model_config = ConfigDict(from_attributes=True)


class CouponListResponse(BaseModel):
    """优惠券列表响应模型"""
    code: int
    message: str
    data: Optional[CouponListData]

    model_config = ConfigDict(from_attributes=True)


# 使用记录模型
class CouponUsageRecordBase(BaseModel):
    coupon_id: int
    coupon_batch_id: Optional[int] = None
    user_id: Optional[int] = None
    order_id: Optional[int] = None
    order_item_id: Optional[int] = None
    discount_amount: Optional[float] = Field(0.0, ge=0, description="优惠金额")
    # status: Optional[CouponUsageStatus] = None


class CouponUsageRecordCreate(CouponUsageRecordBase):
    pass


class CouponUsageRecordBatchCreate(BaseModel):
    """批量创建优惠券使用记录请求模型"""
    records: List[CouponUsageRecordCreate] = Field(..., description="优惠券使用记录列表")


class CouponUsageRecordBatchResponse(BaseModel):
    """批量创建优惠券使用记录响应模型"""
    code: int = 200
    message: str = ""
    data: dict = Field(default_factory=dict, description="批量创建结果")

    model_config = ConfigDict(from_attributes=True)


class CouponUsageRecordResponseData(CouponUsageRecordCreate):
    """优惠券使用记录响应模型"""
    id: int
    used_at: datetime
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

    @field_serializer("used_at", "created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%d %H:%M:%S")


class CouponUsageRecordResponse(BaseModel):
    code: int
    message: str
    data: CouponUsageRecordResponseData

    model_config = ConfigDict(from_attributes=True)


class CouponUsageRecordSearch(BaseModel):
    username: Optional[str] = None
    coupon_name: Optional[str] = None
    order_id: Optional[int] = None


class CouponUsageRecordResponse(BaseModel):
    id: int
    user_id: int
    username: str
    coupon_id: int
    coupon_name: str
    order_id: Optional[int] = None
    discount_amount: float = Field(0.0, ge=0, description="优惠金额")
    used_at: datetime
    # status: CouponUsageStatus
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CouponUsageRecordListResponse(BaseModel):
    code: int = 200
    message: str = ""
    data: List[CouponUsageRecordResponse]


class CouponNameSearchItem(BaseModel):
    """优惠券名称搜索项"""
    name: str
    id: int


class CouponNameSearchResponse(BaseModel):
    """优惠券名称搜索响应"""
    code: int
    message: str
    data: List[CouponNameSearchItem]
